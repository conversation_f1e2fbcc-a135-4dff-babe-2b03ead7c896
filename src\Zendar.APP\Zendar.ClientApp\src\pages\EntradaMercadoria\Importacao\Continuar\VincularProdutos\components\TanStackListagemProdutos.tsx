import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Icon,
  HStack,
  Text,
  Flex,
  useToken,
  Spinner,
  Skeleton,
} from '@chakra-ui/react';
import {
  useReactTable,
  getCoreRowModel,
  getExpandedRowModel,
  ColumnDef,
  flexRender,
  ExpandedState,
  ColumnResizeMode,
} from '@tanstack/react-table';
import { useMemo, useState, useRef, useCallback, useEffect } from 'react';
import { FiCheckCircle, FiChevronUp, FiChevronDown } from 'react-icons/fi';
import {
  AutoSizer,
  List,
  InfiniteLoader,
  Index,
  ListRowProps,
} from 'react-virtualized';

import { DecimalMask } from 'helpers/format/fieldsMasks';
import useWindowSize from 'helpers/layout/useWindowSize';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { LoadMoreRowsParams } from 'components/update/Table/VirtualizedInfiniteTable';

import {
  Produto,
  EntradaMercadoriaStatusVinculoProduto,
  InformacoesRodape,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

import { DetalhesItemProduto } from './DetalhesItemProduto';

interface TanStackListagemProdutosProps {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  isLoading: boolean;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  loadMoreRows: (params: LoadMoreRowsParams) => Promise<void>;
}

export function TanStackListagemProdutos({
  produtos,
  informacoesRodape,
  isLoading,
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  loadMoreRows,
}: TanStackListagemProdutosProps) {
  const [teal600, aquamarine100] = useToken('colors', [
    'teal.600',
    'aquamarine.100',
  ]);
  const { casasDecimais } = usePadronizacaoContext();
  const { width: windowWidth } = useWindowSize();
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [columnSizing, setColumnSizing] = useState({});

  // Virtualização
  const currentPage = useRef(1);
  const isLoadingMore = useRef(false);
  const pageSize = 25;

  // Cálculo de larguras responsivas
  const calculateResponsiveColumnSizes = useMemo(() => {
    const containerPadding = 96; // padding lateral do container
    const availableWidth = windowWidth - containerPadding;

    // Tamanhos mínimos das colunas
    const minSizes = {
      expander: 50,
      produto: 350,
      quantidade: 130,
      valorUnitario: 130,
      valorTotal: 130,
      acoes: 180,
    };

    const totalMinWidth = Object.values(minSizes).reduce(
      (sum, size) => sum + size,
      0
    );

    // Se a tela é menor que o mínimo necessário, usar tamanhos mínimos
    if (availableWidth <= totalMinWidth) {
      return minSizes;
    }

    // Distribuir espaço extra proporcionalmente, priorizando a coluna produto
    const extraSpace = availableWidth - totalMinWidth;
    const produtoExtraSpace = extraSpace * 0.7; // 70% do espaço extra para produto
    const otherColumnsExtraSpace = extraSpace * 0.3; // 30% para outras colunas
    const otherColumnsCount = 3; // quantidade, valorUnitario, valorTotal
    const extraPerOtherColumn = otherColumnsExtraSpace / otherColumnsCount;

    return {
      expander: minSizes.expander,
      produto: minSizes.produto + produtoExtraSpace,
      quantidade: minSizes.quantidade + extraPerOtherColumn,
      valorUnitario: minSizes.valorUnitario + extraPerOtherColumn,
      valorTotal: minSizes.valorTotal + extraPerOtherColumn,
      acoes: minSizes.acoes,
    };
  }, [windowWidth]);

  const obterCorBackground = useCallback(
    (status: EntradaMercadoriaStatusVinculoProduto) => {
      const enumStatus = EntradaMercadoriaStatusVinculoProduto;
      const vinculado = status === enumStatus.VINCULADO;
      const naoVinculado = status === enumStatus.NAO_VINCULADO;

      if (vinculado) return `${teal600} !important`;
      if (naoVinculado) return 'white';
      return `${aquamarine100} !important`;
    },
    [teal600, aquamarine100]
  );

  // Função para carregar dados iniciais
  const loadInitialData = useCallback(async () => {
    if (produtos.length === 0 && !isLoading) {
      currentPage.current = 1;
      await loadMoreRows({
        currentPage: currentPage.current,
        pageSize,
        orderColumn: 'descricaoProdutoNota',
        orderDirection: 'asc',
      });
    }
  }, [produtos.length, isLoading, loadMoreRows, pageSize]);

  // Função para verificar se uma linha está carregada
  const isRowLoaded = useCallback(
    ({ index }: Index) => {
      return !!produtos[index];
    },
    [produtos]
  );

  // Função para carregar mais linhas
  const loadMoreRowsCallback = useCallback(async () => {
    if (isLoadingMore.current || isLoading) return;

    const loadedRowCount = currentPage.current * pageSize;
    if (loadedRowCount >= informacoesRodape.totalProdutos) return;

    isLoadingMore.current = true;
    currentPage.current += 1;

    await loadMoreRows({
      currentPage: currentPage.current,
      pageSize,
      orderColumn: 'descricaoProdutoNota',
      orderDirection: 'asc',
    });

    isLoadingMore.current = false;
  }, [isLoading, informacoesRodape.totalProdutos, loadMoreRows, pageSize]);

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const handleVincularClick = useCallback(
    (produto: Produto, index: number) => {
      const produtoNaoEstaVinculado =
        produto.statusVinculo ===
        EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

      if (produtoNaoEstaVinculado) {
        handleVincularProduto(index);
        return;
      }

      handleVincularProduto(index, {
        id: produto.produtoVinculado?.id || '',
        nome: produto.produtoVinculado?.nome || '',
        tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
        volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
        referencia: produto.produtoVinculado?.referencia || '',
        precoCompra: produto.produtoVinculado?.precoCompra || 0,
        coresOptions: [],
        tamanhosOptions: [],
      });
    },
    [handleVincularProduto]
  );

  const calcularAlturaLinha = useCallback(
    (index: number) => {
      const produto = produtos[index];
      return produto?.isOpen ? 180 : 64;
    },
    [produtos]
  );

  const columns = useMemo<ColumnDef<Produto>[]>(
    () => [
      {
        id: 'expander',
        header: '',
        cell: ({ row }) => {
          const produto = row.original;
          const statusQuePodemMostrarDetalhes = [
            EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
          ];
          const podeMostrarDetalhes =
            statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
            !!produto.dadosAdicionais;

          return podeMostrarDetalhes ? (
            <Button
              bg="transparent"
              p="4px"
              h="fit-content"
              borderRadius="6px"
              minW="16px"
              onClick={() => {
                row.toggleExpanded();
                handleToggleLinhaProduto(row.index);
              }}
            >
              {row.index}
              <Icon
                as={row.getIsExpanded() ? FiChevronUp : FiChevronDown}
                transition="all 0.3s"
              />
            </Button>
          ) : null;
        },
        size: calculateResponsiveColumnSizes.expander,
        minSize: 50,
        enableResizing: false,
      },
      {
        accessorKey: 'descricaoProdutoNota',
        header: 'Produto',
        cell: ({ getValue, row }) => {
          const produto = row.original;
          return (
            <Box>
              <Text fontSize="14px">{getValue() as string}</Text>
              {row.getIsExpanded() && produto.dadosAdicionais && (
                <Box mt="4px" fontSize="12px" fontWeight="bold">
                  <TextoTooltip
                    texto={produto.dadosAdicionais}
                    maxWidth="100%"
                  />
                </Box>
              )}
            </Box>
          );
        },
        size: calculateResponsiveColumnSizes.produto,
        minSize: 350,
        enableResizing: true,
      },
      {
        accessorKey: 'quantidade',
        header: 'Quantidade',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="center">
            {DecimalMask(
              getValue() as number,
              casasDecimais.casasDecimaisQuantidade
            )}
          </Text>
        ),
        size: calculateResponsiveColumnSizes.quantidade,
        minSize: 130,
        enableResizing: true,
      },
      {
        accessorKey: 'valorUnitario',
        header: 'Valor unitário',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="right">
            {DecimalMask(
              getValue() as number,
              casasDecimais.casasDecimaisValor
            )}
          </Text>
        ),
        size: calculateResponsiveColumnSizes.valorUnitario,
        minSize: 130,
        enableResizing: true,
      },
      {
        accessorKey: 'valorTotal',
        header: 'Valor total',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="right">
            {DecimalMask(getValue() as number, 2, 2)}
          </Text>
        ),
        size: calculateResponsiveColumnSizes.valorTotal,
        minSize: 130,
        enableResizing: true,
      },
      {
        id: 'acoes',
        header: 'Ações',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const produtoNaoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

          return produtoEstaVinculado ? (
            <Flex justifyContent="space-between">
              <HStack spacing="1" color="secondary.300">
                <Icon as={FiCheckCircle} boxSize="4" />
                <Text fontSize="xs">Vinculado</Text>
              </HStack>
              <ActionsMenu
                colorScheme="white"
                backgroundHoverColor="gray.500"
                items={[
                  {
                    content: 'Editar',
                    onClick: () => handleEditar(row.index),
                  },
                ]}
              />
            </Flex>
          ) : (
            <Flex alignItems="center" justifyContent="center">
              <Button
                size="xs"
                colorScheme="orange"
                minW="136px"
                onClick={() => handleVincularClick(produto, row.index)}
              >
                {produtoNaoEstaVinculado
                  ? 'Vincular ao sistema'
                  : 'Informar variações'}
              </Button>
            </Flex>
          );
        },
        size: calculateResponsiveColumnSizes.acoes,
        minSize: 180,
        enableResizing: true,
      },
    ],
    [
      casasDecimais,
      handleEditar,
      handleVincularClick,
      handleToggleLinhaProduto,
      calculateResponsiveColumnSizes,
    ]
  );

  const table = useReactTable({
    data: produtos,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: setExpanded,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode: 'onChange' as ColumnResizeMode,
    state: {
      expanded,
      columnSizing,
    },
    getRowId: (_, index) => `produto-${index}`,
  });

  // Largura mínima total da tabela
  const minTableWidth = useMemo(() => {
    return 50 + 350 + 130 + 130 + 130 + 180; // soma dos tamanhos mínimos
  }, []);

  // Largura atual da tabela baseada nos tamanhos responsivos
  const currentTableWidth = useMemo(() => {
    return Object.values(calculateResponsiveColumnSizes).reduce(
      (sum, size) => sum + size,
      0
    );
  }, [calculateResponsiveColumnSizes]);

  // Função para renderizar uma linha da tabela
  const renderRow = useCallback(
    ({ index, key, style }: ListRowProps) => {
      const produto = produtos[index];

      if (!produto) {
        return (
          <div key={key} style={style}>
            <Skeleton h="60px" />
          </div>
        );
      }

      const row = table.getRowModel().rows[index];
      if (!row) return null;

      return (
        <div key={key} style={style}>
          <Table
            variant="simple"
            size="sm"
            bg="gray.50"
            minWidth={`${minTableWidth}px`}
            width={`${currentTableWidth}px`}
            h="60px"
            sx={{
              tableLayout: 'fixed',
              '& thead > tr > th': {
                bg: 'gray.50',
                border: 'none',
              },
              '& td:first-of-type': {
                paddingLeft: '16px !important',
              },
              '& tbody > tr': {
                borderRadius: 'md',
                boxShadow: '0px 0px 2px #00000029',
                ...(informacoesRodape.totalProdutos > 0
                  ? { border: '1px', borderColor: 'gray.100' }
                  : {
                      '& > td': {
                        position: 'relative',
                        _before: {
                          content: '""',
                          position: 'absolute',
                          h: 'full',
                          w: 'full',
                          top: 0,
                          left: 0,
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 'md',
                        },
                      },
                    }),
              },
              '& tbody > tr > td': {
                bg: 'white',
                lineHeight: 'none',
                _before: {
                  border:
                    informacoesRodape.totalProdutos > 0
                      ? 'none !important'
                      : '1px',
                  borderColor: 'gray.100',
                },
              },
            }}
          >
            <Tbody>
              <Tr
                bg={obterCorBackground(produto.statusVinculo)}
                sx={{
                  '& > td': {
                    bg: obterCorBackground(produto.statusVinculo),
                    color:
                      produto.statusVinculo ===
                      EntradaMercadoriaStatusVinculoProduto.VINCULADO
                        ? 'white'
                        : 'inherit',
                  },
                }}
              >
                {row.getVisibleCells().map((cell) => (
                  <Td
                    key={cell.id}
                    width={cell.column.getSize()}
                    minWidth={cell.column.columnDef.minSize}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Td>
                ))}
                {row.getIsExpanded() && (
                  <DetalhesItemProduto
                    produto={produto}
                    obterCorBackground={obterCorBackground}
                  />
                )}
              </Tr>
            </Tbody>
          </Table>
        </div>
      );
    },
    [
      produtos,
      table,
      informacoesRodape.totalProdutos,
      obterCorBackground,
      minTableWidth,
      currentTableWidth,
    ]
  );

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      borderRadius="md"
      border="1px"
      bg="gray.50"
      minH="600px"
      borderColor="gray.200"
      py={{ base: 4, sm: 6, md: 6 }}
      pl={{ base: 4, sm: 6, md: 6 }}
      pr={{ base: '6px', sm: '14px', md: '24px' }}
      width="100%"
      overflowX="auto"
    >
      {/* Header fixo */}
      <Table
        variant="simple"
        size="sm"
        bg="gray.50"
        minWidth={`${minTableWidth}px`}
        width={`${currentTableWidth}px`}
        sx={{
          tableLayout: 'fixed',
          '& thead > tr > th': {
            bg: 'gray.50',
            border: 'none',
            whiteSpace: 'nowrap',
            fontSize: '12px',
            fontWeight: 'bold',
            color: 'gray.600',
            position: 'relative',
          },
        }}
      >
        <Thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Th
                  key={header.id}
                  width={header.getSize()}
                  minWidth={header.column.columnDef.minSize}
                  position="relative"
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                  {/* Resizer */}
                  {header.column.getCanResize() && (
                    <Box
                      position="absolute"
                      right="0"
                      top="0"
                      height="100%"
                      width="4px"
                      bg="transparent"
                      cursor="col-resize"
                      userSelect="none"
                      onMouseDown={header.getResizeHandler()}
                      onTouchStart={header.getResizeHandler()}
                      _hover={{
                        bg: 'blue.300',
                      }}
                      sx={{
                        touchAction: 'none',
                        '&:active': {
                          bg: 'blue.500',
                        },
                      }}
                    />
                  )}
                </Th>
              ))}
            </Tr>
          ))}
        </Thead>
      </Table>

      {/* Lista virtualizada */}
      <Box flex="1" minHeight="300px">
        {informacoesRodape.totalProdutos > 0 ? (
          <InfiniteLoader
            isRowLoaded={isRowLoaded}
            loadMoreRows={loadMoreRowsCallback}
            rowCount={informacoesRodape.totalProdutos}
            threshold={15}
          >
            {({ onRowsRendered, registerChild }) => (
              <AutoSizer>
                {({ width, height }) => (
                  <List
                    ref={registerChild}
                    width={width}
                    height={height}
                    rowCount={informacoesRodape.totalProdutos}
                    rowHeight={({ index }) => {
                      return calcularAlturaLinha(index);
                    }}
                    rowRenderer={renderRow}
                    onRowsRendered={onRowsRendered}
                    overscanRowCount={5}
                  />
                )}
              </AutoSizer>
            )}
          </InfiniteLoader>
        ) : (
          <Box textAlign="center" py={8}>
            <Text color="gray.500">
              {isLoading ? '' : 'Nenhum produto adicionado.'}
            </Text>
            {isLoading && <LoadingPadrao />}
          </Box>
        )}
      </Box>
    </Box>
  );
}
