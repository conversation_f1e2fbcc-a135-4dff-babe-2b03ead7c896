import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Icon,
  HStack,
  Text,
  Flex,
  useToken,
  Skeleton,
} from '@chakra-ui/react';
import {
  useReactTable,
  getCoreRowModel,
  getExpandedRowModel,
  ColumnDef,
  flexRender,
  ExpandedState,
  ColumnResizeMode,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useMemo, useState, useRef, useCallback, useEffect } from 'react';
import { FiCheckCircle, FiChevronUp, FiChevronDown } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';
import useWindowSize from 'helpers/layout/useWindowSize';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';

import {
  Produto,
  EntradaMercadoriaStatusVinculoProduto,
  InformacoesRodape,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

// import { DetalhesItemProduto } from './DetalhesItemProduto'; // TODO: Implementar para linhas expandidas

interface LoadMoreRowsParams {
  currentPage: number;
  pageSize: number;
  orderColumn: string;
  orderDirection: 'asc' | 'desc';
}

interface TanStackListagemProdutosProps {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  isLoading: boolean;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  loadMoreRows: (params: LoadMoreRowsParams) => Promise<void>;
}

export function TanStackListagemProdutos({
  produtos,
  informacoesRodape,
  isLoading,
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  loadMoreRows,
}: TanStackListagemProdutosProps) {
  const [teal600, aquamarine100] = useToken('colors', [
    'teal.600',
    'aquamarine.100',
  ]);
  const { casasDecimais } = usePadronizacaoContext();
  const { width: windowWidth } = useWindowSize();
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [columnSizing, setColumnSizing] = useState({});

  // Referência para o container da tabela (necessário para virtualização)
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Virtualização
  const currentPage = useRef(1);
  const isLoadingMore = useRef(false);
  const pageSize = 25;

  // Definição de larguras das colunas com tamanhos mínimos fixos
  const columnSizes = useMemo(() => {
    // Tamanhos mínimos fixos para cada coluna
    const minSizes = {
      expander: 60,
      produto: 600, // Coluna produto com maior largura
      quantidade: 120,
      valorUnitario: 140,
      valorTotal: 140,
      acoes: 200,
    };

    const totalMinWidth = Object.values(minSizes).reduce(
      (sum, size) => sum + size,
      0
    );

    const containerPadding = 208; // padding lateral do container
    const availableWidth = windowWidth - containerPadding;

    // Se há espaço extra disponível, distribuir priorizando a coluna produto
    if (availableWidth > totalMinWidth) {
      const extraSpace = availableWidth - totalMinWidth;
      const produtoExtraSpace = extraSpace * 0.8; // 80% do espaço extra para produto
      const otherColumnsExtraSpace = extraSpace * 0.2; // 20% para outras colunas
      const otherColumnsCount = 3; // quantidade, valorUnitario, valorTotal
      const extraPerOtherColumn = otherColumnsExtraSpace / otherColumnsCount;

      return {
        expander: minSizes.expander,
        produto: minSizes.produto + produtoExtraSpace,
        quantidade: minSizes.quantidade + extraPerOtherColumn,
        valorUnitario: minSizes.valorUnitario + extraPerOtherColumn,
        valorTotal: minSizes.valorTotal + extraPerOtherColumn,
        acoes: minSizes.acoes,
      };
    }

    // Sempre retornar os tamanhos mínimos (scroll horizontal aparecerá se necessário)
    return minSizes;
  }, [windowWidth]);

  const obterCorBackground = useCallback(
    (status: EntradaMercadoriaStatusVinculoProduto) => {
      const enumStatus = EntradaMercadoriaStatusVinculoProduto;
      const vinculado = status === enumStatus.VINCULADO;
      const naoVinculado = status === enumStatus.NAO_VINCULADO;

      if (vinculado) return `${teal600} !important`;
      if (naoVinculado) return 'white';
      return `${aquamarine100} !important`;
    },
    [teal600, aquamarine100]
  );

  // Função para carregar dados iniciais
  const loadInitialData = useCallback(async () => {
    if (produtos.length === 0 && !isLoading) {
      currentPage.current = 1;
      await loadMoreRows({
        currentPage: currentPage.current,
        pageSize,
        orderColumn: 'descricaoProdutoNota',
        orderDirection: 'asc',
      });
    }
  }, [produtos.length, isLoading, loadMoreRows, pageSize]);

  // Função para carregar mais dados quando o usuário rola para o final
  const fetchMoreOnBottomReached = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        const hasMorePages = produtos.length < informacoesRodape.totalProdutos;

        // Quando o usuário rolar até 300px do final da tabela, carregar mais dados
        if (
          scrollHeight - scrollTop - clientHeight < 300 &&
          hasMorePages &&
          !isLoading &&
          !isLoadingMore.current
        ) {
          isLoadingMore.current = true;
          currentPage.current += 1;

          loadMoreRows({
            currentPage: currentPage.current,
            pageSize,
            orderColumn: 'descricaoProdutoNota',
            orderDirection: 'asc',
          }).finally(() => {
            isLoadingMore.current = false;
          });
        }
      }
    },
    [
      isLoading,
      informacoesRodape.totalProdutos,
      loadMoreRows,
      pageSize,
      produtos.length,
    ]
  );

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  // Verificar se precisa carregar mais dados ao montar e após fetch
  useEffect(() => {
    fetchMoreOnBottomReached(tableContainerRef.current);
  }, [fetchMoreOnBottomReached]);

  const handleVincularClick = useCallback(
    (produto: Produto, index: number) => {
      const produtoNaoEstaVinculado =
        produto.statusVinculo ===
        EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

      if (produtoNaoEstaVinculado) {
        handleVincularProduto(index);
        return;
      }

      handleVincularProduto(index, {
        id: produto.produtoVinculado?.id || '',
        nome: produto.produtoVinculado?.nome || '',
        tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
        volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
        referencia: produto.produtoVinculado?.referencia || '',
        precoCompra: produto.produtoVinculado?.precoCompra || 0,
        coresOptions: [],
        tamanhosOptions: [],
      });
    },
    [handleVincularProduto]
  );

  // Configurar o virtualizer do TanStack
  const rowVirtualizer = useVirtualizer({
    count: informacoesRodape.totalProdutos, // Usar o total de produtos, não apenas os carregados
    getScrollElement: () => tableContainerRef.current,
    estimateSize: (index) => {
      const produto = produtos[index];
      // Altura mínima de 64px, expandida 180px
      return produto?.isOpen ? 180 : 64;
    },
    measureElement:
      typeof window !== 'undefined' &&
      navigator.userAgent.indexOf('Firefox') === -1
        ? (element) => element?.getBoundingClientRect().height
        : undefined,
    overscan: 5,
    // Garantir que o scroll seja suave
    scrollMargin: 0,
  });

  // Função para carregar mais dados quando um item não carregado fica visível
  const loadMoreIfNeeded = useCallback(
    (index: number) => {
      const lastLoadedIndex = produtos.length - 1;
      const hasMorePages = produtos.length < informacoesRodape.totalProdutos;

      // Só carregar se:
      // 1. O item está próximo do final dos dados carregados
      // 2. Ainda há mais páginas para carregar
      // 3. Não está carregando no momento
      // 4. Não está carregando mais dados
      if (
        index >= lastLoadedIndex - 3 && // 3 itens antes do final dos dados carregados
        hasMorePages &&
        !isLoading &&
        !isLoadingMore.current
      ) {
        isLoadingMore.current = true;
        currentPage.current += 1;

        loadMoreRows({
          currentPage: currentPage.current,
          pageSize,
          orderColumn: 'descricaoProdutoNota',
          orderDirection: 'asc',
        }).finally(() => {
          isLoadingMore.current = false;
        });
      }
    },
    [
      produtos.length,
      informacoesRodape.totalProdutos,
      isLoading,
      loadMoreRows,
      pageSize,
    ]
  );

  const columns = useMemo<ColumnDef<Produto>[]>(
    () => [
      {
        id: 'expander',
        header: '',
        cell: ({ row }) => {
          const produto = row.original;
          const statusQuePodemMostrarDetalhes = [
            EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
          ];
          const podeMostrarDetalhes =
            statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
            !!produto.dadosAdicionais;

          return podeMostrarDetalhes ? (
            <Button
              bg="transparent"
              p="4px"
              h="fit-content"
              borderRadius="6px"
              minW="16px"
              onClick={() => {
                row.toggleExpanded();
                handleToggleLinhaProduto(row.index);
              }}
            >
              <Icon
                as={row.getIsExpanded() ? FiChevronUp : FiChevronDown}
                transition="all 0.3s"
              />
            </Button>
          ) : null;
        },
        size: columnSizes.expander,
        minSize: 60,
        enableResizing: false,
      },
      {
        accessorKey: 'descricaoProdutoNota',
        header: 'Produto',
        cell: ({ getValue, row }) => {
          const produto = row.original;
          return (
            <Box>
              <Text fontSize="14px">{getValue() as string}</Text>
              {row.getIsExpanded() && produto.dadosAdicionais && (
                <Box mt="4px" fontSize="12px" fontWeight="bold">
                  <TextoTooltip
                    texto={produto.dadosAdicionais}
                    maxWidth="100%"
                  />
                </Box>
              )}
            </Box>
          );
        },
        size: columnSizes.produto,
        minSize: 600,
        enableResizing: true,
      },
      {
        accessorKey: 'quantidade',
        header: 'Quantidade',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="center">
            {DecimalMask(
              getValue() as number,
              casasDecimais.casasDecimaisQuantidade
            )}
          </Text>
        ),
        size: columnSizes.quantidade,
        minSize: 120,
        enableResizing: true,
      },
      {
        accessorKey: 'valorUnitario',
        header: 'Valor unitário',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="right">
            {DecimalMask(
              getValue() as number,
              casasDecimais.casasDecimaisValor
            )}
          </Text>
        ),
        size: columnSizes.valorUnitario,
        minSize: 140,
        enableResizing: true,
      },
      {
        accessorKey: 'valorTotal',
        header: 'Valor total',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="right">
            {DecimalMask(getValue() as number, 2, 2)}
          </Text>
        ),
        size: columnSizes.valorTotal,
        minSize: 140,
        enableResizing: true,
      },
      {
        id: 'acoes',
        header: 'Ações',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const produtoNaoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

          return produtoEstaVinculado ? (
            <Flex justifyContent="space-between">
              <HStack spacing="1" color="secondary.300">
                <Icon as={FiCheckCircle} boxSize="4" />
                <Text fontSize="xs">Vinculado</Text>
              </HStack>
              <ActionsMenu
                colorScheme="white"
                backgroundHoverColor="gray.500"
                items={[
                  {
                    content: 'Editar',
                    onClick: () => handleEditar(row.index),
                  },
                ]}
              />
            </Flex>
          ) : (
            <Flex alignItems="center" justifyContent="center">
              <Button
                size="xs"
                colorScheme="orange"
                minW="136px"
                onClick={() => handleVincularClick(produto, row.index)}
              >
                {produtoNaoEstaVinculado
                  ? 'Vincular ao sistema'
                  : 'Informar variações'}
              </Button>
            </Flex>
          );
        },
        size: columnSizes.acoes,
        minSize: 200,
        enableResizing: true,
      },
    ],
    [
      casasDecimais,
      handleEditar,
      handleVincularClick,
      handleToggleLinhaProduto,
      columnSizes,
    ]
  );

  const table = useReactTable({
    data: produtos,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: setExpanded,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode: 'onChange' as ColumnResizeMode,
    state: {
      expanded,
      columnSizing,
    },
    getRowId: (_, index) => `produto-${index}`,
  });

  // Largura mínima total da tabela
  const minTableWidth = useMemo(() => {
    return 60 + 600 + 120 + 140 + 140 + 200; // soma dos tamanhos mínimos
  }, []);

  // Largura atual da tabela baseada nos tamanhos das colunas
  const currentTableWidth = useMemo(() => {
    return Object.values(columnSizes).reduce((sum, size) => sum + size, 0);
  }, [columnSizes]);

  return (
    <Box
      display="flex"
      flexDirection="column"
      borderRadius="md"
      border="1px"
      bg="gray.50"
      minH="600px"
      borderColor="gray.200"
      py={{ base: 4, sm: 6, md: 6 }}
      pl={{ base: 4, sm: 6, md: 6 }}
      pr={{ base: '6px', sm: '14px', md: '24px' }}
      width="100%"
    >
      {/* Container principal com scroll sincronizado */}
      <Box
        ref={tableContainerRef}
        flex="1"
        height="600px"
        overflowY="auto"
        overflowX="auto"
        position="relative"
        onScroll={(e) => fetchMoreOnBottomReached(e.currentTarget)}
        sx={{
          '&::-webkit-scrollbar': {
            height: '8px',
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            bg: 'gray.100',
          },
          '&::-webkit-scrollbar-thumb': {
            bg: 'gray.300',
            borderRadius: '4px',
          },
        }}
      >
        {informacoesRodape.totalProdutos > 0 ? (
          /* Tabela completa usando CSS Grid como no exemplo oficial */
          <Table
            style={{ display: 'grid' }}
            variant="simple"
            size="sm"
            bg="gray.50"
            minWidth={`${minTableWidth}px`}
            width={`${currentTableWidth}px`}
            sx={{
              tableLayout: 'fixed',
            }}
          >
            {/* Cabeçalho fixo */}
            <Thead
              style={{
                display: 'grid',
                position: 'sticky',
                top: 0,
                zIndex: 1,
              }}
            >
              {table.getHeaderGroups().map((headerGroup) => (
                <Tr
                  key={headerGroup.id}
                  style={{ display: 'flex', width: '100%' }}
                >
                  {headerGroup.headers.map((header) => (
                    <Th
                      key={header.id}
                      style={{
                        display: 'flex',
                        width: header.getSize(),
                        minWidth: header.column.columnDef.minSize,
                      }}
                      sx={{
                        bg: 'gray.50',
                        border: 'none',
                        whiteSpace: 'nowrap',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: 'gray.600',
                        position: 'relative',
                      }}
                    >
                      <Box
                        flex="1"
                        alignItems="center"
                        display="flex"
                        justifyContent={
                          header.column.id === 'acoes' ||
                          header.column.id === 'valorTotal'
                            ? 'flex-end'
                            : header.column.id === 'valorUnitario'
                            ? 'center'
                            : 'flex-start'
                        }
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </Box>
                      {/* Resizer */}
                      {header.column.getCanResize() && (
                        <Box
                          position="absolute"
                          right="0"
                          top="0"
                          height="100%"
                          width="4px"
                          bg="transparent"
                          cursor="col-resize"
                          userSelect="none"
                          onMouseDown={header.getResizeHandler()}
                          onTouchStart={header.getResizeHandler()}
                          _hover={{
                            bg: 'blue.300',
                          }}
                          sx={{
                            touchAction: 'none',
                            '&:active': {
                              bg: 'blue.500',
                            },
                          }}
                        />
                      )}
                    </Th>
                  ))}
                </Tr>
              ))}
            </Thead>

            {/* Corpo da tabela virtualizado */}
            <Tbody
              style={{
                display: 'grid',
                height: `${rowVirtualizer.getTotalSize() + 20}px`, // +20px para garantir que o último item seja visível
                position: 'relative',
              }}
            >
              {rowVirtualizer.getVirtualItems().map((virtualRow) => {
                const produto = produtos[virtualRow.index];
                if (!produto) {
                  // Disparar carregamento quando item não carregado fica visível
                  loadMoreIfNeeded(virtualRow.index);

                  return (
                    <Tr
                      key={virtualRow.key}
                      data-index={virtualRow.index}
                      ref={(node) => rowVirtualizer.measureElement(node)}
                      style={{
                        display: 'flex',
                        position: 'absolute',
                        transform: `translateY(${virtualRow.start}px)`,
                        width: '100%',
                        minHeight: '64px',
                      }}
                      sx={{
                        borderRadius: 'md',
                        boxShadow: '0px 0px 2px #00000029',
                        border: '1px',
                        borderColor: 'gray.100',
                        mb: '2px',
                      }}
                    >
                      {/* Skeleton para cada coluna com tamanhos idênticos */}
                      <Td
                        style={{
                          display: 'flex',
                          width: columnSizes.expander,
                          minWidth: 60,
                          alignItems: 'center',
                          minHeight: '64px',
                        }}
                        sx={{
                          bg: 'white',
                          border: 'none',
                          paddingLeft: '16px',
                          paddingRight: '8px',
                        }}
                      >
                        <Skeleton h="20px" w="20px" />
                      </Td>

                      <Td
                        style={{
                          display: 'flex',
                          width: columnSizes.produto,
                          minWidth: 400,
                          alignItems: 'center',
                          minHeight: '64px',
                        }}
                        sx={{
                          bg: 'white',
                          border: 'none',
                          paddingLeft: '8px',
                          paddingRight: '8px',
                        }}
                      >
                        <Skeleton h="16px" w="80%" />
                      </Td>

                      <Td
                        style={{
                          display: 'flex',
                          width: columnSizes.quantidade,
                          minWidth: 120,
                          alignItems: 'center',
                          minHeight: '64px',
                          justifyContent: 'center',
                        }}
                        sx={{
                          bg: 'white',
                          border: 'none',
                          paddingLeft: '8px',
                          paddingRight: '8px',
                        }}
                      >
                        <Skeleton h="16px" w="60px" />
                      </Td>

                      <Td
                        style={{
                          display: 'flex',
                          width: columnSizes.valorUnitario,
                          minWidth: 140,
                          alignItems: 'center',
                          minHeight: '64px',
                          justifyContent: 'flex-end',
                        }}
                        sx={{
                          bg: 'white',
                          border: 'none',
                          paddingLeft: '8px',
                          paddingRight: '8px',
                        }}
                      >
                        <Skeleton h="16px" w="80px" />
                      </Td>

                      <Td
                        style={{
                          display: 'flex',
                          width: columnSizes.valorTotal,
                          minWidth: 140,
                          alignItems: 'center',
                          minHeight: '64px',
                          justifyContent: 'flex-end',
                        }}
                        sx={{
                          bg: 'white',
                          border: 'none',
                          paddingLeft: '8px',
                          paddingRight: '8px',
                        }}
                      >
                        <Skeleton h="16px" w="80px" />
                      </Td>

                      <Td
                        style={{
                          display: 'flex',
                          width: columnSizes.acoes,
                          minWidth: 200,
                          alignItems: 'center',
                          minHeight: '64px',
                          justifyContent: 'center',
                        }}
                        sx={{
                          bg: 'white',
                          border: 'none',
                          paddingLeft: '8px',
                          paddingRight: '8px',
                        }}
                      >
                        <Skeleton h="24px" w="136px" borderRadius="4px" />
                      </Td>
                    </Tr>
                  );
                }

                const row = table.getRowModel().rows[virtualRow.index];
                if (!row) return null;

                return (
                  <Tr
                    key={virtualRow.key}
                    data-index={virtualRow.index}
                    ref={(node) => rowVirtualizer.measureElement(node)}
                    style={{
                      display: 'flex',
                      position: 'absolute',
                      transform: `translateY(${virtualRow.start}px)`,
                      width: '100%',
                      minHeight: '64px',
                    }}
                    sx={{
                      borderRadius: 'md',
                      boxShadow: '0px 0px 2px #00000029',
                      border: '1px',
                      borderColor: 'gray.100',
                      mb: '2px',
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <Td
                        key={cell.id}
                        style={{
                          display: 'flex',
                          width: cell.column.getSize(),
                          minWidth: cell.column.columnDef.minSize,
                          alignItems: 'center',
                          minHeight: '64px',
                          paddingLeft: '16px',
                          paddingRight: '16px',
                          justifyContent:
                            cell.column.id === 'acoes' ||
                            cell.column.id === 'valorTotal'
                              ? 'flex-end'
                              : cell.column.id === 'valorUnitario'
                              ? 'center'
                              : 'flex-start',
                        }}
                        sx={{
                          bg: obterCorBackground(produto.statusVinculo),
                          color:
                            produto.statusVinculo ===
                            EntradaMercadoriaStatusVinculoProduto.VINCULADO
                              ? 'white'
                              : 'inherit',
                          border: 'none',
                          paddingLeft:
                            cell.column.id === 'expander' ? '16px' : '8px',
                          paddingRight: '8px',
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </Td>
                    ))}
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        ) : (
          <Box textAlign="center" py={8}>
            <Text color="gray.500">
              {isLoading ? '' : 'Nenhum produto adicionado.'}
            </Text>
            {isLoading && <LoadingPadrao />}
          </Box>
        )}
      </Box>
    </Box>
  );
}
