import { Box, ChakraProps } from '@chakra-ui/react';
import React, { ReactNode } from 'react';

interface BodyProps extends ChakraProps {
  children: ReactNode;
  editarManualmente?: boolean;
}

export function Body({ children, editarManualmente, ...rest }: BodyProps) {
  return (
    <Box
      {...rest}
      py={editarManualmente ? undefined : { base: 4, sm: 6, md: 6 }}
      w="full"
      h="full"
      overflowX="auto"
    >
      {children}
    </Box>
  );
}
